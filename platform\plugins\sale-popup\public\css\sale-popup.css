.sale-popup-container-wrap {
  max-width: 350px;
  position: fixed;
  top: auto;
  background-color: #fff;
  transition: all 0.25s ease;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.12);
  border-radius: 5px;
  inset-inline-start: 35px;
  bottom: 55px;
  width: 100%;
  z-index: 350;
}
.sale-popup-container-wrap.hidden {
  display: none;
}

.sale-popup-container {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  margin-left: auto;
  margin-right: auto;
}

.sale-popup-thumb {
  padding: 10px;
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}
.sale-popup-thumb .js-sale-popup-a {
  position: relative;
  overflow: hidden;
  display: block;
}
.sale-popup-thumb img {
  max-width: 65px;
}

.sale-popup-close {
  position: absolute;
}

.sale-popup-quick-view {
  position: absolute !important;
  bottom: 0 !important;
  top: auto !important;
  margin: 10px 0;
}

.sale-popup-info {
  max-width: 265px;
  padding: 10px 10px 10px 0 !important;
  flex-basis: 0;
  flex-grow: 1;
}
.sale-popup-info .sale-popup-location {
  display: block;
  font-size: 14px;
  margin-bottom: 5px;
  color: #686666;
  font-weight: 400;
}
.sale-popup-info .sale-popup-location .js-sale-popup-location {
  font-size: 15px;
  color: #000;
}

.sale-popup-title {
  font-weight: 600;
  color: #222;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
  text-transform: uppercase;
  margin-bottom: 5px;
}

.sale-popup-ago {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.sale-popup-ago .sale-popup-verify {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #000;
  gap: 4px;
}
.sale-popup-ago .sale-popup-time {
  color: #686666;
  font-size: 14px;
}
.sale-popup-ago svg {
  color: #0ca940;
  width: 1.25rem;
  height: 1.25rem;
}

.sale-popup-container-wrap.hidden {
  display: none;
}
.sale-popup-container-wrap a.pa {
  inset-inline-end: 5px;
  top: 12px;
  text-align: center;
  opacity: 1;
  display: inline-block;
  line-height: 25px;
  width: 25px;
  height: 25px;
  font-size: 20px;
  border-radius: 5px;
  font-weight: 400;
}
.sale-popup-container-wrap a.sale-popup-quick-view {
  top: auto;
  bottom: 12px;
}
.sale-popup-container-wrap.des_2, .sale-popup-container-wrap.des_2 a {
  border-radius: 90px;
}
.sale-popup-container-wrap.des_2 .sale-popup-thumb > a {
  border-radius: 50%;
}
.sale-popup-container-wrap.des_2 a.pa {
  top: 50%;
  margin-top: -25px;
}
.sale-popup-container-wrap a.sale-popup-quick-view {
  bottom: auto;
  margin-top: 0;
}

@media (max-width: 767px) {
  .sale-popup-container-wrap {
    inset-inline-start: 3px;
    bottom: 5px;
  }
}
@media (max-width: 375px) {
  .sale-popup-info {
    max-width: 210px;
  }
}
@media (max-width: 370px) {
  .sale-popup-container-wrap {
    inset-inline-start: 0;
    max-width: 320px;
  }
  .sale-popup-info {
    max-width: 230px;
  }
}
.sales_animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}
@keyframes slideOutDown {
  from {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  to {
    visibility: hidden;
    transform: translate3d(0, 100%, 0);
    opacity: 0;
  }
}
.slideOutDown {
  animation-name: slideOutDown;
}
@keyframes slideOutLeft {
  from {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  to {
    visibility: hidden;
    transform: translate3d(-100%, 0, 0);
    opacity: 0;
  }
}
.slideOutLeft {
  animation-name: slideOutLeft;
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.fadeOut {
  animation-name: fadeOut;
}
@keyframes fadeOutLeft {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
}
.fadeOutLeft {
  animation-name: fadeOutLeft;
}
@keyframes bounceOutDown {
  20% {
    transform: translate3d(0, 10px, 0);
  }
  40%, 45% {
    opacity: 1;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(0, 2000px, 0);
  }
}
.bounceOutDown {
  animation-name: bounceOutDown;
}
@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0);
  }
}
.bounceOutLeft {
  animation-name: bounceOutLeft;
}
@keyframes rotateOutDownLeft {
  from {
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}
.rotateOutDownLeft {
  animation-name: rotateOutDownLeft;
}
@keyframes rotateOutDownLeft {
  from {
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}
.rotateOutDownLeft {
  animation-name: rotateOutDownLeft;
}
@keyframes flipOutX {
  from {
    transform: perspective(400px);
  }
  30% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}
.flipOutX {
  animation-duration: 0.75s;
  animation-name: flipOutX;
  backface-visibility: visible !important;
}
@keyframes zoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
.zoomOut {
  animation-name: zoomOut;
}
@keyframes rollOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}
.rollOut {
  animation-name: rollOut;
}
@keyframes bounceOutDown {
  20% {
    transform: translate3d(0, 10px, 0);
  }
  40%, 45% {
    opacity: 1;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(0, 2000px, 0);
  }
}
.bounceOutDown {
  animation-name: bounceOutDown;
}

@keyframes slideInUp {
  from {
    transform: translate3d(0, 100%, 0);
    visibility: visible;
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.slideInUp {
  animation-name: slideInUp;
}
@keyframes slideInLeft {
  from {
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.slideInLeft {
  animation-name: slideInLeft;
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.fadeIn {
  animation-name: fadeIn;
}
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.fadeInLeft {
  animation-name: fadeInLeft;
}
@keyframes bounceInUp {
  60%, 75%, 90%, from, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    transform: translate3d(0, 3000px, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(0, -20px, 0);
  }
  75% {
    transform: translate3d(0, 10px, 0);
  }
  90% {
    transform: translate3d(0, -5px, 0);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.bounceInUp {
  animation-name: bounceInUp;
}
@keyframes bounceInLeft {
  60%, 75%, 90%, from, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: translate3d(-3000px, 0, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(25px, 0, 0);
  }
  75% {
    transform: translate3d(-10px, 0, 0);
  }
  90% {
    transform: translate3d(5px, 0, 0);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.bounceInLeft {
  animation-name: bounceInLeft;
}
@keyframes rotateInDownLeft {
  from {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
  to {
    transform-origin: left bottom;
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.rotateInDownLeft {
  animation-name: rotateInDownLeft;
}
@keyframes rotateInUpLeft {
  from {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    transform-origin: left bottom;
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.rotateInUpLeft {
  animation-name: rotateInUpLeft;
}
@keyframes flipInX {
  from {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    animation-timing-function: ease-in;
  }
  60% {
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  to {
    transform: perspective(400px);
  }
}
.flipInX {
  backface-visibility: visible !important;
  animation-name: flipInX;
}
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}
.zoomIn {
  animation-name: zoomIn;
}
@keyframes rollIn {
  from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.rollIn {
  animation-name: rollIn;
}
@keyframes swing {
  20% {
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
.swing {
  transform-origin: top center;
  animation-name: swing;
}
@keyframes shake {
  from, to {
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    transform: translate3d(10px, 0, 0);
  }
}
.shake {
  animation-name: shake;
}
@keyframes wobble {
  from {
    transform: translate3d(0, 0, 0);
  }
  15% {
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.wobble {
  animation-name: wobble;
}
@keyframes jello {
  11.1%, from, to {
    transform: translate3d(0, 0, 0);
  }
  22.2% {
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }
  88.8% {
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}
.jello {
  animation-name: jello;
  transform-origin: center;
}
