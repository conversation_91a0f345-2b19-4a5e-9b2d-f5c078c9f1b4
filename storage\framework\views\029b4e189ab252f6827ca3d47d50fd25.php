<?php if(is_plugin_active('ecommerce')): ?>
    <?php
        $mobile ??= false;
    ?>

    <?php if(isset($currencies) && count($currencies) > 1): ?>
        <?php if($mobile): ?>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-usd-circle"></i>
                    <?php echo e(get_application_currency()->title); ?>

                </a>
                <ul class="dropdown-menu">
                    <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li>
                            <a class="dropdown-item" href="<?php echo e(route('public.change-currency', $currency->title)); ?>"><?php echo e($currency->title); ?></a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </li>
        <?php else: ?>
            <div class="headertoplag__lang">
                <ul>
                    <li>
                        <button>
                            <?php echo e(get_application_currency()->title); ?>

                            <span><i class="fal fa-angle-down"></i></span>
                        </button>
                        <ul class="header-meta__lang-submenu">
                            <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li>
                                    <a href="<?php echo e(route('public.change-currency', $currency->title)); ?>"><?php echo e($currency->title); ?></a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </li>
                </ul>
            </div>
        <?php endif; ?>
    <?php endif; ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\main\platform\themes/ninico/partials/currency-switcher.blade.php ENDPATH**/ ?>