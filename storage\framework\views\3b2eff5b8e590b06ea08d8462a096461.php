<?php if(is_plugin_active('ecommerce')): ?>
    <div class="header-search-bar">
        <form action="<?php echo e(route('public.products')); ?>" class="position-relative form--quick-search" data-url="<?php echo e(route('public.ajax.search-products')); ?>" method="GET">
            <div class="search-info p-relative">
                <?php if($hasCategorySelect = (theme_option('display_product_categories_select_on_header', 'yes') === 'yes')): ?>
                    <div class="product-category-label">
                        <label class="form-label" for="product-category-select" class="text text-truncate d-inline-block"><?php echo e(__('All Categories')); ?></label>
                        <i class="fal fa-chevron-down ms-2"></i>
                    </div>
                    <select
                        class="product-category-select"
                        name="categories[]"
                    >
                        <option value="0"><?php echo e(__('All Categories')); ?></option>
                        <?php echo ProductCategoryHelper::renderProductCategoriesSelect(); ?>

                    </select>
                <?php endif; ?>
                <input type="text" name="q" class="<?php echo \Illuminate\Support\Arr::toCssClasses(['input-search-product', 'has-category-select' => $hasCategorySelect]); ?>" placeholder="<?php echo e(__('Search products...')); ?>" value="<?php echo e(BaseHelper::stringify(request()->query('q'))); ?>" autocomplete="off">
                <button class="header-search-icon" title="search">
                    <i class="fal fa-search"></i>
                </button>
            </div>
            <div class="panel--search-result"></div>
        </form>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\main\platform\themes/ninico/partials/header-search-bar.blade.php ENDPATH**/ ?>