<div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['header-top', $class ?? 'space-bg']); ?>">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-xl-8 col-lg-12 col-md-12">
                <?php if(is_plugin_active('announcement') && ($announcements = \ArchiElite\Announcement\AnnouncementHelper::getAnnouncements()) && $announcements->isNotEmpty()): ?>
                    <div
                        class="ae-anno-announcement-wrapper"
                        style="
                            --background-color: <?php echo e(setting('announcement_background_color', theme_option('primary_color', '#000'))); ?>;
                            --text-color: <?php echo e(setting('announcement_text_color', '#fff')); ?>;
                            --font-size: <?php echo e(\ArchiElite\Announcement\AnnouncementHelper::getFontSize()); ?>;
                        "
                        <?php if(setting('announcement_autoplay', true)): ?>
                            data-announcement-autoplay="<?php echo e(setting('announcement_autoplay', false)); ?>"
                        data-announcement-autoplay-delay="<?php echo e(setting('announcement_autoplay_delay', 5000)); ?>"
                        <?php endif; ?>
                    >
                        <div
                            class="ae-anno-announcement__items"
                            style="
                                justify-content: <?php echo e(\ArchiElite\Announcement\AnnouncementHelper::getTextAlignment()); ?>;
                                <?php if(setting('announcement_text_alignment') === \ArchiElite\Announcement\Enums\TextAlignment::CENTER): ?> text-align: center; <?php endif; ?>
                               max-width: <?php echo e(\ArchiElite\Announcement\AnnouncementHelper::getMaxWidth()); ?>;
                            "
                        >
                            <?php if($announcements->count() > 1): ?>
                                <?php echo $__env->make('plugins/announcement::partials.controls', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php endif; ?>

                            <?php $__currentLoopData = $announcements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $announcement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div
                                    data-announcement-id="<?php echo e($announcement->getKey()); ?>"
                                    style="display: none;"
                                    class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                        'ae-anno-announcement',
                                        'ae-anno-announcement__bottom' => \ArchiElite\Announcement\AnnouncementHelper::isBottomPlacement(),
                                    ]); ?>"
                                >
                                    <div class="ae-anno-announcement__content header-welcome-text">
                                        <div class="ae-anno-announcement__text">
                                            <span><?php echo BaseHelper::clean($announcement->formatted_content); ?></span>
                                        </div>

                                        <?php if($announcement->has_action): ?>
                                            <a
                                                class="ae-anno-announcement__button"
                                                href="<?php echo e($announcement->action_url); ?>"
                                                <?php if($announcement->action_open_new_tab): ?> target="_blank" <?php endif; ?>
                                            >
                                                <?php echo BaseHelper::clean($announcement->action_label); ?>

                                                <i class="fal fa-long-arrow-right"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <?php if(setting('announcement_dismissible', false)): ?>
                            <?php echo $__env->make('plugins/announcement::partials.dismiss', [
                                'announcementIds' => $announcements->pluck('id')->toJson(),
                            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <?php if(theme_option('header_messages') && $headerMessages = json_decode(theme_option('header_messages'), true)): ?>
                        <?php ($message = collect($headerMessages[array_rand($headerMessages)])->pluck('value', 'key')); ?>
                        <?php if($message->get('message') && $message->get('link_text')): ?>
                            <div class="header-welcome-text">
                                <span><?php echo BaseHelper::clean($message->get('message')); ?></span>
                                <a href="<?php echo e($message->get('link')); ?>" class="ms-2">
                                    <?php echo BaseHelper::clean($message->get('link_text')); ?> <i class="fal fa-long-arrow-right"></i>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            <div class="col-xl-4 d-none d-xl-block">
                <div class="headertoplag d-flex align-items-center justify-content-end">
                    <?php echo Theme::partial('currency-switcher'); ?>

                    <?php echo Theme::partial('language-switcher'); ?>

                    <?php if(theme_option('social_links') && $socialLinks = json_decode(theme_option('social_links'), true)): ?>
                        <div class="menu-top-social">
                            <?php $__currentLoopData = $socialLinks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $socialLink): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php ($socialLink = collect($socialLink)->pluck('value', 'key')); ?>
                                <a href="<?php echo e($socialLink->get('url')); ?>" title="<?php echo e($socialLink->get('name')); ?>">
                                    <?php echo BaseHelper::renderIcon($socialLink->get('icon')); ?>

                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\main\platform\themes/ninico/partials/header-top.blade.php ENDPATH**/ ?>